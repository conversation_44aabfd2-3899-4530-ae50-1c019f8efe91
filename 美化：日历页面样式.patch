Subject: [PATCH] 美化：日历页面样式
---
Index: miniprogram/pages/calendar/index.wxml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/miniprogram/pages/calendar/index.wxml b/miniprogram/pages/calendar/index.wxml
--- a/miniprogram/pages/calendar/index.wxml	(revision fde32f4e2d80e1b2f4b055e87a973de47e695ae2)
+++ b/miniprogram/pages/calendar/index.wxml	(date 1754014540494)
@@ -126,7 +126,7 @@
 
       <!-- 时间可视化 -->
       <view class="chart-section" wx:if="{{displaySegments.length > 0}}">
-        <view class="section-title">
+        <view class="section-title-simple">
           <text class="section-icon">📊</text>
           <text>时间可视化</text>
         </view>
@@ -142,7 +142,7 @@
 
       <!-- 时间统计 -->
       <view class="time-stats-section" wx:if="{{displaySegments.length > 0}}">
-        <view class="section-title">
+        <view class="section-title-simple">
           <text class="section-icon">⏱️</text>
           <text>时间统计</text>
         </view>
@@ -176,7 +176,7 @@
 
       <!-- 时间安排 -->
       <view class="segments-section">
-        <view class="section-title">
+        <view class="section-title-simple">
           <text class="section-icon">🕐</text>
           <text>时间安排</text>
         </view>
@@ -216,51 +216,72 @@
       </view>
     </view>
 
-    <!-- 摸鱼记录卡片 -->
+    <!-- 摸鱼卡片 -->
     <view class="fishing-records-card" wx:if="{{selectedDate}}">
       <view class="section-title">
-        <text class="section-icon">🐟</text>
-        <text>摸鱼记录</text>
-        <view class="add-fishing-btn" bind:tap="onAddFishing">
+        <view class="section-title-left">
+          <text class="section-icon">🐟</text>
+          <text>摸鱼</text>
+        </view>
+        <view class="add-adjustment-btn fishing" bind:tap="onAddFishing">
           <text class="add-icon">+</text>
           <text class="add-text">添加</text>
         </view>
       </view>
 
-      <view wx:if="{{displayFishes.length > 0}}" class="fishing-list">
-        <view class="fishing-item" wx:for="{{displayFishes}}" wx:key="id">
-          <view class="fishing-left">
-            <view class="fishing-badge">🐟</view>
-            <view class="fishing-time">
-              <text class="time-text">{{item.startTime}} - {{item.endTime}}</text>
-              <text class="duration-text">{{item.duration}}</text>
+      <!-- 摸鱼统计 -->
+      <view class="fishing-stats-section" wx:if="{{displayFishes.length > 0}}">
+        <view class="section-title-simple">
+          <text class="section-icon">📊</text>
+          <text>摸鱼统计</text>
+        </view>
+
+        <view class="time-stats-grid">
+          <view class="time-stat-item fishing-count-stat">
+            <view class="stat-icon">🐟</view>
+            <view class="stat-info">
+              <view class="stat-label">摸鱼次数</view>
+              <view class="stat-value">{{fishingStats.count}}次</view>
             </view>
           </view>
 
-          <view class="fishing-right">
-            <view class="fishing-remark">
-              <text class="remark-text">{{item.remark || '日常摸鱼'}}</text>
+          <view class="time-stat-item fishing-duration-stat">
+            <view class="stat-icon">⏱️</view>
+            <view class="stat-info">
+              <view class="stat-label">摸鱼时长</view>
+              <view class="stat-value">{{fishingStats.duration}}</view>
             </view>
+          </view>
 
-            <!-- 摸鱼价值信息 -->
-            <view class="fishing-value">
-              <view class="value-item">
-                <text class="value-label">时薪:</text>
-                <text class="value-text">¥{{item.hourlyRateText}}/小时</text>
-              </view>
-              <view class="value-item">
-                <text class="value-label">价值:</text>
-                <text class="value-text">¥{{item.fishingValueText}}</text>
-              </view>
-            </view>
-
-            <view class="fishing-actions">
-              <view class="action-btn-small edit" bind:tap="onEditFishing" data-id="{{item.id}}">
-                <text class="btn-icon">✏️</text>
-              </view>
-              <view class="action-btn-small delete" bind:tap="onDeleteFishing" data-id="{{item.id}}">
-                <text class="btn-icon">🗑️</text>
+          <view class="time-stat-item fishing-income-stat">
+            <view class="stat-icon">💰</view>
+            <view class="stat-info">
+              <view class="stat-label">摸鱼收入</view>
+              <view class="stat-value">¥{{fishingStats.income}}</view>
+            </view>
+          </view>
+        </view>
+      </view>
+
+      <view wx:if="{{displayFishes.length > 0}}" class="fishing-list">
+        <view class="fishing-item" wx:for="{{displayFishes}}" wx:key="id" bind:tap="onEditFishing" data-id="{{item.id}}">
+          <view class="fishing-left">
+            <view class="type-badge type-fishing">🐟</view>
+            <view class="fishing-content {{item.remark ? '' : 'no-subtitle'}}">
+              <view class="fishing-title">
+                <text class="time-text">{{item.startTime}} - {{item.endTime}}</text>
+                <text class="duration-text">{{item.duration}}</text>
               </view>
+              <view wx:if="{{item.remark}}" class="fishing-subtitle">
+                <text class="remark-text">{{item.remark}}</text>
+              </view>
+            </view>
+          </view>
+
+          <view class="fishing-right">
+            <view class="fishing-value">
+              <text class="value-text">¥{{item.hourlyRateText}}/h</text>
+              <text class="rate-text">¥{{item.fishingValueText}}</text>
             </view>
           </view>
         </view>
@@ -276,8 +297,10 @@
     <!-- 收入调整卡片 -->
     <view class="income-adjustment-card" wx:if="{{selectedDate}}">
       <view class="section-title">
-        <text class="section-icon">💰</text>
-        <text>收入调整</text>
+        <view class="section-title-left">
+          <text class="section-icon">💰</text>
+          <text>收入调整</text>
+        </view>
         <view class="adjustment-actions">
           <view class="add-adjustment-btn income" bind:tap="onAddExtraIncome">
             <text class="add-icon">+</text>
@@ -290,21 +313,37 @@
         </view>
       </view>
 
-      <!-- 收入调整汇总 -->
-      <view wx:if="{{selectedDayAdjustmentSummary}}" class="adjustment-summary">
-        <view class="summary-item" wx:if="{{selectedDayAdjustmentSummary.extraIncome > 0}}">
-          <text class="summary-label">额外收入:</text>
-          <text class="summary-value positive">+¥{{selectedDayAdjustmentSummary.extraIncome}}</text>
+      <!-- 收入调整统计 -->
+      <view wx:if="{{selectedDayAdjustmentSummary && (selectedDayAdjustmentSummary.extraIncomeItems.length > 0 || selectedDayAdjustmentSummary.deductionItems.length > 0)}}" class="adjustment-stats-section">
+        <view class="section-title-simple">
+          <text class="section-icon">📊</text>
+          <text>调整统计</text>
         </view>
-        <view class="summary-item" wx:if="{{selectedDayAdjustmentSummary.deductions > 0}}">
-          <text class="summary-label">扣款:</text>
-          <text class="summary-value negative">-¥{{selectedDayAdjustmentSummary.deductions}}</text>
-        </view>
-        <view class="summary-item" wx:if="{{selectedDayAdjustmentSummary.netAdjustment !== 0}}">
-          <text class="summary-label">结余:</text>
-          <text class="summary-value {{selectedDayAdjustmentSummary.netAdjustment > 0 ? 'positive' : 'negative'}}">
-            {{selectedDayAdjustmentSummary.netAdjustment > 0 ? '+' : ''}}¥{{selectedDayAdjustmentSummary.netAdjustment}}
-          </text>
+
+        <view class="time-stats-grid">
+          <view class="time-stat-item income-stat" wx:if="{{selectedDayAdjustmentSummary.extraIncome > 0}}">
+            <view class="stat-icon">📈</view>
+            <view class="stat-info">
+              <view class="stat-label">额外收入</view>
+              <view class="stat-value">+¥{{selectedDayAdjustmentSummary.extraIncome}}</view>
+            </view>
+          </view>
+
+          <view class="time-stat-item deduction-stat" wx:if="{{selectedDayAdjustmentSummary.deductions > 0}}">
+            <view class="stat-icon">📉</view>
+            <view class="stat-info">
+              <view class="stat-label">扣款</view>
+              <view class="stat-value">-¥{{selectedDayAdjustmentSummary.deductions}}</view>
+            </view>
+          </view>
+
+          <view class="time-stat-item net-stat" wx:if="{{selectedDayAdjustmentSummary.netAdjustment !== 0}}">
+            <view class="stat-icon">{{selectedDayAdjustmentSummary.netAdjustment > 0 ? '💰' : '💸'}}</view>
+            <view class="stat-info">
+              <view class="stat-label">结余</view>
+              <view class="stat-value">{{selectedDayAdjustmentSummary.netAdjustment > 0 ? '+' : ''}}¥{{selectedDayAdjustmentSummary.netAdjustment}}</view>
+            </view>
+          </view>
         </view>
       </view>
 
@@ -1137,6 +1176,7 @@
     fishing-data="{{editingFishing}}"
     segments="{{displaySegments}}"
     bind:save="onFishingEditorSave"
+    bind:delete="onFishingEditorDelete"
     bind:cancel="onFishingEditorCancel"
     bind:close="onFishingEditorClose">
   </fishing-editor>
Index: miniprogram/pages/calendar/index.js
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/miniprogram/pages/calendar/index.js b/miniprogram/pages/calendar/index.js
--- a/miniprogram/pages/calendar/index.js	(revision fde32f4e2d80e1b2f4b055e87a973de47e695ae2)
+++ b/miniprogram/pages/calendar/index.js	(date 1754014633371)
@@ -144,6 +144,13 @@
     restTime: '0小时',
     overtimeTime: '0小时',
 
+    // 摸鱼统计
+    fishingStats: {
+      count: 0,
+      duration: '0分钟',
+      income: '0.00'
+    },
+
     // 引导界面
     hasWorkHistory: true,
 
@@ -639,6 +646,9 @@
     // 更新时间统计
     this.updateTimeStatistics()
 
+    // 更新摸鱼统计
+    this.updateFishingStatistics(displayFishes)
+
     // 获取选中日期的节假日信息
     let selectedDateInfo = { holidayInfo: null }
     if (this.holidayManager && this.data.selectedDate) {
@@ -736,6 +746,54 @@
     })
   },
 
+  /**
+   * 更新摸鱼统计信息
+   */
+  updateFishingStatistics(displayFishes) {
+    if (!displayFishes || displayFishes.length === 0) {
+      this.setData({
+        fishingStats: {
+          count: 0,
+          duration: '0分钟',
+          income: '0.00'
+        }
+      })
+      return
+    }
+
+    // 计算摸鱼次数
+    const count = displayFishes.length
+
+    // 计算摸鱼总时长
+    let totalMinutes = 0
+    let totalIncome = 0
+
+    displayFishes.forEach(fish => {
+      // 计算时长（从原始数据）
+      const duration = fish.end - fish.start
+      totalMinutes += duration
+
+      // 计算收入
+      if (fish.fishingValue) {
+        totalIncome += fish.fishingValue
+      }
+    })
+
+    // 格式化时长
+    const duration = this.formatTimeMinutes(totalMinutes)
+
+    // 格式化收入
+    const income = totalIncome.toFixed(2)
+
+    this.setData({
+      fishingStats: {
+        count,
+        duration,
+        income
+      }
+    })
+  },
+
   /**
    * 格式化分钟为时分显示
    */
@@ -3588,6 +3646,35 @@
       })
     }
   },
+
+  /**
+   * 摸鱼编辑器删除事件
+   */
+  onFishingEditorDelete(e) {
+    const { fishingData } = e.detail
+
+    try {
+      // 删除摸鱼记录
+      this.deleteFishingRecord(fishingData.id)
+
+      // 关闭编辑器
+      this.setData({
+        showFishingEditor: false,
+        editingFishing: null
+      })
+
+      wx.showToast({
+        title: '删除成功',
+        icon: 'success'
+      })
+    } catch (error) {
+      console.error('删除摸鱼记录失败:', error)
+      wx.showToast({
+        title: '删除失败',
+        icon: 'error'
+      })
+    }
+  },
 
   /**
    * 摸鱼编辑器取消事件
Index: miniprogram/pages/calendar/index.wxss
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/miniprogram/pages/calendar/index.wxss b/miniprogram/pages/calendar/index.wxss
--- a/miniprogram/pages/calendar/index.wxss	(revision fde32f4e2d80e1b2f4b055e87a973de47e695ae2)
+++ b/miniprogram/pages/calendar/index.wxss	(date 1754014564526)
@@ -595,6 +595,30 @@
   background: linear-gradient(90deg, #fef3c7, #f59e0b);
 }
 
+.fishing-count-stat::before {
+  background: linear-gradient(90deg, #fef3c7, #fbbf24);
+}
+
+.fishing-duration-stat::before {
+  background: linear-gradient(90deg, #fef3c7, #f59e0b);
+}
+
+.fishing-income-stat::before {
+  background: linear-gradient(90deg, #d1fae5, #10b981);
+}
+
+.income-stat::before {
+  background: linear-gradient(90deg, #d1fae5, #10b981);
+}
+
+.deduction-stat::before {
+  background: linear-gradient(90deg, #fee2e2, #ef4444);
+}
+
+.net-stat::before {
+  background: linear-gradient(90deg, #dbeafe, #3b82f6);
+}
+
 .stat-icon {
   font-size: 40rpx;
   margin-bottom: 12rpx;
@@ -628,6 +652,22 @@
 .section-title {
   display: flex;
   align-items: center;
+  justify-content: space-between;
+  font-size: 28rpx;
+  font-weight: 600;
+  color: #2d3748;
+  margin-bottom: 16rpx;
+  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
+}
+
+.section-title-left {
+  display: flex;
+  align-items: center;
+}
+
+.section-title-simple {
+  display: flex;
+  align-items: center;
   font-size: 28rpx;
   font-weight: 600;
   color: #2d3748;
@@ -944,36 +984,66 @@
   flex: 1;
 }
 
-.fishing-badge {
+.type-badge.type-fishing {
+  background-color: #fbbf24;
+  color: #fff;
   padding: 6rpx 12rpx;
-  border-radius: 20rpx;
-  font-size: 22rpx;
-  background: #fbbf24;
-  color: #fff;
+  border-radius: 12rpx;
+  font-size: 20rpx;
+  font-weight: 600;
+  min-width: 60rpx;
+  text-align: center;
 }
 
-.fishing-time {
+.fishing-content {
   display: flex;
   flex-direction: column;
   gap: 4rpx;
+  flex: 1;
+}
+
+.fishing-content.no-subtitle {
+  justify-content: center;
+}
+
+.fishing-title {
+  display: flex;
+  flex-direction: column;
+  gap: 2rpx;
+}
+
+.fishing-subtitle {
+  margin-top: 4rpx;
+}
+
+.remark-text {
+  font-size: 22rpx;
+  color: #6b7280;
+  word-break: break-all;
+  line-height: 1.4;
 }
 
 .fishing-right {
+  display: flex;
+  align-items: center;
+}
+
+.fishing-value {
   display: flex;
   flex-direction: column;
   align-items: flex-end;
-  gap: 8rpx;
-}
-
-.fishing-remark {
-  max-width: 200rpx;
+  gap: 4rpx;
 }
 
-.remark-text {
+.fishing-value .value-text {
   font-size: 24rpx;
+  color: #10b981;
+  font-weight: 600;
+}
+
+.fishing-value .rate-text {
+  font-size: 20rpx;
   color: #6b7280;
-  word-break: break-all;
-  text-align: right;
 }
 
 .fishing-actions {
@@ -3207,6 +3277,17 @@
   background-color: #ffcdd2;
 }
 
+.add-adjustment-btn.fishing {
+  background-color: #fef3c7;
+  color: #d97706;
+  border: 1rpx solid #fbbf24;
+}
+
+.add-adjustment-btn.fishing:active {
+  background-color: #fbbf24;
+  color: #fff;
+}
+
 .add-adjustment-btn .add-icon {
   font-size: 20rpx;
   margin-right: 8rpx;
Index: miniprogram/components/fishing-editor/index.wxml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/miniprogram/components/fishing-editor/index.wxml b/miniprogram/components/fishing-editor/index.wxml
--- a/miniprogram/components/fishing-editor/index.wxml	(revision fde32f4e2d80e1b2f4b055e87a973de47e695ae2)
+++ b/miniprogram/components/fishing-editor/index.wxml	(date 1754013965891)
@@ -61,14 +61,14 @@
 
     <!-- 操作按钮 -->
     <view class="editor-footer">
-      <button 
-        class="editor-btn cancel-btn" 
-        bindtap="onCancel"
+      <button
+        class="editor-btn {{mode === 'edit' ? 'delete-btn' : 'cancel-btn'}}"
+        bindtap="{{mode === 'edit' ? 'onDelete' : 'onCancel'}}"
       >
-        取消
+        {{mode === 'edit' ? '删除' : '取消'}}
       </button>
-      <button 
-        class="editor-btn save-btn" 
+      <button
+        class="editor-btn save-btn"
         disabled="{{loading}}"
         bindtap="onSave"
       >
Index: miniprogram/components/fishing-editor/index.js
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/miniprogram/components/fishing-editor/index.js b/miniprogram/components/fishing-editor/index.js
--- a/miniprogram/components/fishing-editor/index.js	(revision fde32f4e2d80e1b2f4b055e87a973de47e695ae2)
+++ b/miniprogram/components/fishing-editor/index.js	(date 1754013984665)
@@ -368,6 +368,30 @@
       this.triggerEvent('cancel')
     },
 
+    /**
+     * 删除摸鱼记录
+     */
+    onDelete() {
+      if (this.data.mode !== 'edit' || !this.data.fishingData) {
+        return
+      }
+
+      wx.showModal({
+        title: '确认删除',
+        content: '确定要删除这条摸鱼记录吗？',
+        confirmText: '删除',
+        confirmColor: '#dc2626',
+        success: (res) => {
+          if (res.confirm) {
+            // 触发删除事件
+            this.triggerEvent('delete', {
+              fishingData: this.data.fishingData
+            })
+          }
+        }
+      })
+    },
+
     /**
      * 关闭编辑器
      */
Index: miniprogram/components/fishing-editor/index.wxss
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/miniprogram/components/fishing-editor/index.wxss b/miniprogram/components/fishing-editor/index.wxss
--- a/miniprogram/components/fishing-editor/index.wxss	(revision fde32f4e2d80e1b2f4b055e87a973de47e695ae2)
+++ b/miniprogram/components/fishing-editor/index.wxss	(date 1754013973722)
@@ -225,6 +225,15 @@
   background: #e6e6e6;
 }
 
+.delete-btn {
+  background: #dc2626;
+  color: #fff;
+}
+
+.delete-btn:active {
+  background: #b91c1c;
+}
+
 .save-btn {
   background: #1890ff;
   color: #fff;
