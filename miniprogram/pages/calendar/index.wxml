<!--日历页面 - 集成版本-->
<view class="calendar-container">
  <!-- 没有工作履历时的引导界面 -->
  <view wx:if="{{!hasWorkHistory}}" class="no-work-guide">
    <view class="guide-content">
      <view class="guide-icon">📅</view>
      <view class="guide-title">设置工作计划</view>
      <view class="guide-text">
        <text>要开始使用工作计划功能，</text>
        <text>请先添加您的工作履历。</text>
      </view>
      <button class="guide-btn" bindtap="goToWorkHistory">
        <text class="btn-icon">✨</text>
        <text>添加工作履历</text>
      </button>
    </view>
  </view>

  <!-- 有工作履历时的正常日历界面 -->
  <view wx:else class="calendar-content">
    <!-- 头部 -->
    <view class="calendar-header">
      <view class="header-title">
        <text class="title-icon">📅</text>
        <text class="title-text">工作计划</text>
      </view>

      <!-- 当前工作履历显示 -->
      <view class="current-work-info">
        <view class="work-info-content">
          <text class="work-icon">💼</text>
          <text class="work-name">{{currentWorkDisplayName}}</text>
        </view>
        <view class="work-switch-btn" bindtap="goToWorkHistory">
          <text>切换</text>
        </view>
      </view>
    </view>

    <!-- 日历主体 -->
    <view class="calendar-main">
      <!-- 月份导航 -->
      <view class="month-nav">
        <view class="nav-btn" bind:tap="onPreviousYear">
          <text class="year-nav-icon">‹‹</text>
        </view>
        <view class="nav-btn" bind:tap="onPreviousMonth">
          <text class="month-nav-icon">‹</text>
        </view>
        
        <view class="month-title">
          <text class="year">{{currentYear}}年</text>
          <text class="month">{{currentMonth}}月</text>
        </view>
        
        <view class="nav-btn" bind:tap="onNextMonth">
          <text class="month-nav-icon">›</text>
        </view>
        <view class="nav-btn" bind:tap="onNextYear">
          <text class="year-nav-icon">››</text>
        </view>
      </view>

      <!-- 星期标题 -->
      <view class="weekdays">
        <view class="weekday" wx:for="{{weekdays}}" wx:key="index">
          <text>{{item}}</text>
        </view>
      </view>

      <!-- 日历网格 -->
      <view class="calendar-grid">
        <view
          class="day-cell {{item ? 'day-active' : 'day-empty'}} {{item && item.isToday ? 'day-today' : ''}} {{item && item.isSelected ? 'day-selected' : ''}} {{item && item.hasData ? 'day-has-data' : ''}} {{item && !item.isInEmploymentRange ? 'day-outside-employment' : ''}} {{item && item.status ? 'day-status-' + item.status : ''}}"
          wx:for="{{calendarDays}}"
          wx:key="index"
          bind:tap="onDateTap"
          data-day="{{item ? item.day : ''}}"
          data-holiday-type="{{item && item.holidayInfo ? item.holidayInfo.type : 'normal'}}"
          style="{{item && item.statusConfig ? '--status-color: ' + item.statusConfig.color + '; --status-bg-color: ' + item.statusConfig.backgroundColor + '; --status-border-color: ' + item.statusConfig.borderColor + ';' : ''}}">
          
          <text wx:if="{{item}}" class="day-number">{{item.day}}</text>
          
          <!-- 状态指示器 -->
          <view wx:if="{{item && item.status && item.statusConfig}}" class="status-indicator">
            <text class="status-icon">{{item.statusConfig.icon}}</text>
          </view>
          
          <!-- 节假日指示器 -->
          <view wx:if="{{item && item.holidayInfo && item.holidayInfo.type !== 'normal' && item.holidayInfo.type !== 'weekend'}}" class="holiday-indicator">
            <text class="holiday-text">{{item.holidayInfo.name}}</text>
          </view>
          
          <!-- 数据指示点 - 已移除，改用状态背景色 -->
          
          <!-- 今天标识 -->
          <view wx:if="{{item && item.isToday}}" class="today-indicator">今</view>

          <!-- 发薪日标识 -->
          <view wx:if="{{item && item.isPayDay}}" class="payday-indicator">💰</view>
        </view>
      </view>
    </view>

    <!-- 日期信息卡片 -->
    <view class="date-info-card" wx:if="{{selectedDate}}">
      <view class="day-header">
        <view class="day-title">
          <text class="date-icon">📋</text>
          <text class="date-text">{{selectedDateText}}</text>
        </view>

        <view class="day-income" wx:if="{{selectedDayData}}">
          <text class="income-label">日收入:</text>
          <text class="income-value">¥{{selectedDayData.dailyIncomeText || '0.00'}}</text>
        </view>
      </view>

      <!-- 节假日信息显示 -->
      <view wx:if="{{selectedDateInfo.holidayInfo && selectedDateInfo.holidayInfo.type !== 'normal'}}" class="holiday-info-display">
        <view class="holiday-badge holiday-{{selectedDateInfo.holidayInfo.type}}">
          <text class="holiday-name">{{selectedDateInfo.holidayInfo.name}}</text>
          <text class="holiday-work-status">{{selectedDateInfo.holidayInfo.isWork ? '工作日' : '休息日'}}</text>
        </view>
      </view>

      <!-- 时间可视化 -->
      <view class="chart-section" wx:if="{{displaySegments.length > 0}}">
        <view class="section-title-simple">
          <text class="section-icon">📊</text>
          <text>时间可视化</text>
        </view>

        <time-chart
          segments="{{chartSegments}}"
          fishes="{{displayFishes}}"
          selected-date="{{selectedDateKey}}"
          height="{{140}}"
          show-current-time="{{true}}">
        </time-chart>
      </view>

      <!-- 时间统计 -->
      <view class="time-stats-section" wx:if="{{displaySegments.length > 0}}">
        <view class="section-title-simple">
          <text class="section-icon">⏱️</text>
          <text>时间统计</text>
        </view>

        <view class="time-stats-grid">
          <view class="time-stat-item work-stat">
            <view class="stat-icon">💼</view>
            <view class="stat-info">
              <view class="stat-label">工作时间</view>
              <view class="stat-value">{{workTime}}</view>
            </view>
          </view>

          <view class="time-stat-item rest-stat">
            <view class="stat-icon">☕</view>
            <view class="stat-info">
              <view class="stat-label">休息时间</view>
              <view class="stat-value">{{restTime}}</view>
            </view>
          </view>

          <view class="time-stat-item overtime-stat">
            <view class="stat-icon">🌙</view>
            <view class="stat-info">
              <view class="stat-label">加班时间</view>
              <view class="stat-value">{{overtimeTime}}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 时间安排 -->
      <view class="segments-section">
        <view class="section-title-simple">
          <text class="section-icon">🕐</text>
          <text>时间安排</text>
        </view>

        <view wx:if="{{displaySegments.length === 0}}" class="empty-segments">
          <view class="empty-icon">📝</view>
          <view class="empty-text">暂无时间安排</view>
          <view class="empty-tip">点击下方按钮设置工作计划</view>
        </view>

        <view wx:else class="segments-list">
          <view class="segment-item segment-{{item.type}}" wx:for="{{displaySegments}}" wx:key="id">
            <view class="segment-left">
              <view class="type-badge type-{{item.type}}">{{item.typeText}}</view>
              <view class="segment-time">
                <text class="time-text">{{item.startTime}} - {{item.endTime}}</text>
                <text class="duration-text">{{item.duration}}</text>
              </view>
            </view>

            <view class="segment-right" wx:if="{{item.income > 0}}">
              <view class="segment-income">
                <text class="income-text">¥{{item.incomeText}}</text>
                <text class="rate-text" wx:if="{{item.hourlyRateText}}">¥{{item.hourlyRateText}}/h</text>
              </view>
            </view>
          </view>
        </view>

        <!--  按钮 -->
        <view class="schedule-actions">
          <view class="action-btn primary" bind:tap="onSetSchedule">
            <text class="btn-icon">⚙️</text>
            <text>{{selectedDayData && selectedDayData.segments.length > 0 ? '编辑计划' : '设置计划'}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 摸鱼卡片 -->
    <view class="fishing-records-card" wx:if="{{selectedDate}}">
      <view class="section-title">
        <view class="section-title-left">
          <text class="section-icon">🐟</text>
          <text>摸鱼</text>
        </view>
        <view class="add-adjustment-btn fishing" bind:tap="onAddFishing">
          <text class="add-icon">+</text>
          <text class="add-text">添加</text>
        </view>
      </view>

      <!-- 摸鱼统计 -->
      <view class="fishing-stats-section" wx:if="{{displayFishes.length > 0}}">
        <view class="section-title-simple">
          <text class="section-icon">📊</text>
          <text>摸鱼统计</text>
        </view>

        <view class="time-stats-grid">
          <view class="time-stat-item fishing-count-stat">
            <view class="stat-icon">🐟</view>
            <view class="stat-info">
              <view class="stat-label">摸鱼次数</view>
              <view class="stat-value">{{fishingStats.count}}次</view>
            </view>
          </view>

          <view class="time-stat-item fishing-duration-stat">
            <view class="stat-icon">⏱️</view>
            <view class="stat-info">
              <view class="stat-label">摸鱼时长</view>
              <view class="stat-value">{{fishingStats.duration}}</view>
            </view>
          </view>

          <view class="time-stat-item fishing-income-stat">
            <view class="stat-icon">💰</view>
            <view class="stat-info">
              <view class="stat-label">摸鱼收入</view>
              <view class="stat-value">¥{{fishingStats.income}}</view>
            </view>
          </view>
        </view>
      </view>

      <view wx:if="{{displayFishes.length > 0}}" class="fishing-list">
        <view class="fishing-item" wx:for="{{displayFishes}}" wx:key="id" bind:tap="onEditFishing" data-id="{{item.id}}">
          <view class="fishing-left">
            <view class="type-badge type-fishing">🐟</view>
            <view class="fishing-content {{item.remark ? '' : 'no-subtitle'}}">
              <view class="fishing-title">
                <text class="time-text">{{item.startTime}} - {{item.endTime}}</text>
                <text class="duration-text">{{item.duration}}</text>
              </view>
              <view wx:if="{{item.remark}}" class="fishing-subtitle">
                <text class="remark-text">{{item.remark}}</text>
              </view>
            </view>
          </view>

          <view class="fishing-right">
            <view class="fishing-value">
              <text class="value-text">¥{{item.hourlyRateText}}/h</text>
              <text class="rate-text">¥{{item.fishingValueText}}</text>
            </view>
          </view>
        </view>
      </view>

      <view wx:else class="empty-fishing">
        <text class="empty-icon">🐟</text>
        <text class="empty-text">暂无摸鱼记录</text>
        <text class="empty-tip">点击上方"添加"按钮创建摸鱼记录</text>
      </view>
    </view>

    <!-- 收入调整卡片 -->
    <view class="income-adjustment-card" wx:if="{{selectedDate}}">
      <view class="section-title">
        <view class="section-title-left">
          <text class="section-icon">💰</text>
          <text>收入调整</text>
        </view>
        <view class="adjustment-actions">
          <view class="add-adjustment-btn income" bind:tap="onAddExtraIncome">
            <text class="add-icon">+</text>
            <text class="add-text">收入</text>
          </view>
          <view class="add-adjustment-btn deduction" bind:tap="onAddDeduction">
            <text class="add-icon">-</text>
            <text class="add-text">扣款</text>
          </view>
        </view>
      </view>

      <!-- 收入调整统计 -->
      <view wx:if="{{selectedDayAdjustmentSummary && (selectedDayAdjustmentSummary.extraIncomeItems.length > 0 || selectedDayAdjustmentSummary.deductionItems.length > 0)}}" class="adjustment-stats-section">
        <view class="section-title-simple">
          <text class="section-icon">📊</text>
          <text>调整统计</text>
        </view>

        <view class="time-stats-grid">
          <view class="time-stat-item income-stat" wx:if="{{selectedDayAdjustmentSummary.extraIncome > 0}}">
            <view class="stat-icon">📈</view>
            <view class="stat-info">
              <view class="stat-label">额外收入</view>
              <view class="stat-value">+¥{{selectedDayAdjustmentSummary.extraIncome}}</view>
            </view>
          </view>

          <view class="time-stat-item deduction-stat" wx:if="{{selectedDayAdjustmentSummary.deductions > 0}}">
            <view class="stat-icon">📉</view>
            <view class="stat-info">
              <view class="stat-label">扣款</view>
              <view class="stat-value">-¥{{selectedDayAdjustmentSummary.deductions}}</view>
            </view>
          </view>

          <view class="time-stat-item net-stat" wx:if="{{selectedDayAdjustmentSummary.netAdjustment !== 0}}">
            <view class="stat-icon">{{selectedDayAdjustmentSummary.netAdjustment > 0 ? '💰' : '💸'}}</view>
            <view class="stat-info">
              <view class="stat-label">结余</view>
              <view class="stat-value">{{selectedDayAdjustmentSummary.netAdjustment > 0 ? '+' : ''}}¥{{selectedDayAdjustmentSummary.netAdjustment}}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 额外收入列表 -->
      <view wx:if="{{selectedDayAdjustmentSummary && selectedDayAdjustmentSummary.extraIncomeItems.length > 0}}" class="adjustment-list">
        <view class="adjustment-category-title">
          <text class="category-icon">📈</text>
          <text>额外收入</text>
        </view>
        <view class="adjustment-item income-item" wx:for="{{selectedDayAdjustmentSummary.extraIncomeItems}}" wx:key="id">
          <view class="adjustment-left">
            <view class="adjustment-type">{{item.typeText}}</view>
            <view wx:if="{{item.desc}}" class="adjustment-description">{{item.desc}}</view>
          </view>
          <view class="adjustment-right">
            <view class="adjustment-amount positive">+¥{{item.amount || 0}}</view>
            <view class="adjustment-actions">
              <view class="action-btn-small edit" bind:tap="onEditExtraIncome" data-item="{{item}}">
                <text class="btn-icon">✏️</text>
              </view>
              <view class="action-btn-small delete" bind:tap="onDeleteExtraIncome" data-id="{{item.id}}">
                <text class="btn-icon">🗑️</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 扣款列表 -->
      <view wx:if="{{selectedDayAdjustmentSummary && selectedDayAdjustmentSummary.deductionItems.length > 0}}" class="adjustment-list">
        <view class="adjustment-category-title">
          <text class="category-icon">📉</text>
          <text>扣款</text>
        </view>
        <view class="adjustment-item deduction-item" wx:for="{{selectedDayAdjustmentSummary.deductionItems}}" wx:key="id">
          <view class="adjustment-left">
            <view class="adjustment-type">{{item.typeText}}</view>
            <view wx:if="{{item.desc}}" class="adjustment-description">{{item.desc}}</view>
          </view>
          <view class="adjustment-right">
            <view class="adjustment-amount negative">-¥{{item.amount || 0}}</view>
            <view class="adjustment-actions">
              <view class="action-btn-small edit" bind:tap="onEditDeduction" data-item="{{item}}">
                <text class="btn-icon">✏️</text>
              </view>
              <view class="action-btn-small delete" bind:tap="onDeleteDeduction" data-id="{{item.id}}">
                <text class="btn-icon">🗑️</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view wx:if="{{!selectedDayAdjustmentSummary || (selectedDayAdjustmentSummary.extraIncomeItems.length === 0 && selectedDayAdjustmentSummary.deductionItems.length === 0)}}" class="empty-adjustments">
        <text class="empty-icon">💰</text>
        <text class="empty-text">暂无收入调整</text>
        <text class="empty-tip">点击上方按钮添加额外收入或扣款</text>
      </view>
    </view>

    <!-- 危险操作按钮卡片 -->
    <view class="danger-actions-card" wx:if="{{selectedDate && selectedDayData && selectedDayData.segments.length > 0}}">
      <view class="danger-actions">
        <view class="action-btn danger" bind:tap="onClearDay">
          <text class="btn-icon">🗑️</text>
          <text>清除当日所有数据</text>
        </view>
        <view class="danger-tip">
          <text class="tip-icon">⚠️</text>
          <text class="tip-text">此操作将清除当日的时间安排、摸鱼记录和收入调整等所有数据，请谨慎操作</text>
        </view>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="quick-actions">
      <view class="action-btn secondary" bind:tap="onToday">
        <text class="btn-icon">🏠</text>
        <text>回到今天</text>
      </view>
      <view class="action-btn secondary" bind:tap="onBatchCopy">
        <text class="btn-icon">📋</text>
        <text>批量复制</text>
      </view>
      <!-- 临时调试按钮 -->
      <view class="action-btn secondary" bind:tap="debugForceUpdateHolidays">
        <text class="btn-icon">🔄</text>
        <text>更新节假日</text>
      </view>
    </view>
  </view>

  <!-- 设置工作计划模态框 -->
  <view class="modal {{scheduleModalVisible ? 'show' : ''}}" wx:if="{{showScheduleModal}}" bind:tap="onCloseScheduleModal">
    <view class="modal-content" catch:tap="onStopPropagation">
      <view class="modal-header">
        <view class="modal-title">设置工作计划</view>
        <view class="modal-close" bind:tap="onCloseScheduleModal">×</view>
      </view>
      
      <view class="modal-body">
        <view class="modal-date">{{selectedDateText}}</view>
        
        <!-- 日期状态设置 -->
        <view class="input-group">
          <view class="input-label">日期状态</view>
          <view class="status-selector" bind:tap="onOpenDateTypeSelector">
            <view class="picker-text">
              <text class="status-emoji">{{selectedStatusConfig.icon}}</text>
              <text>{{selectedStatusConfig.text}}</text>
              <text class="selector-arrow">›</text>
            </view>
          </view>
          <view class="input-tip">设置当日的状态类型，会在日历中用不同颜色显示</view>
        </view>
        

        
        <!-- 时间段设置 -->
        <view class="input-group">
          <view class="input-label">
            <text>时间安排</text>
            <view class="time-summary">
              <view class="smart-income-btn" bind:tap="onSmartIncomeCalculate">
                <text class="btn-icon">✨</text>
                <text>智能填写</text>
              </view>
            </view>
          </view>

          <!-- 时间段问题提示 -->
          <view class="conflict-warning" wx:if="{{hasTimeConflict}}">
            <text class="warning-icon">⚠️</text>
            <text class="warning-text">检测到时间段设置问题，请调整后再保存</text>
          </view>

          <!-- 时间段列表 -->
          <view class="time-inputs" wx:if="{{timeInputs.length > 0}}">
            <view class="time-segment-card {{item.hasConflict ? 'conflict' : ''}}" wx:for="{{timeInputs}}" wx:key="index">
              <!-- 时间段头部 -->
              <view class="segment-header">
                <!-- 时间段类型 -->
                <picker class="segment-type-picker"
                        range="{{typeOptions}}"
                        range-key="text"
                        value="{{item.typeIndex}}"
                        bind:change="onTypeChange"
                        data-index="{{index}}">
                  <view class="segment-type-display">
                    <text class="type-icon">{{typeOptions[item.typeIndex].icon || '🕐'}}</text>
                    <text class="type-text">{{typeOptions[item.typeIndex].text}}</text>
                  </view>
                </picker>

                <!-- 时间段时长 -->
                <view class="segment-duration">
                  <text class="duration-value">{{item.durationText}}</text>
                </view>

                <!-- 删除按钮 -->
                <view class="segment-remove-btn" bind:tap="onRemoveTimeInput" data-index="{{index}}">
                  <text class="remove-icon">×</text>
                </view>
              </view>

              <!-- 时间设置区域 -->
              <view class="time-setting-row">
                <view class="time-range-selector" bind:tap="onOpenTimeRangePicker" data-index="{{index}}">
                  <view class="time-range-display">
                    <view class="time-range-text">
                      <text class="start-time">{{item.startTime}}</text>
                      <text class="start-next-day-indicator" wx:if="{{item.isStartNextDay}}">次日</text>
                      <text class="time-separator">-</text>
                      <text class="end-time">{{item.endTime}}</text>
                      <text class="end-next-day-indicator" wx:if="{{item.isEndNextDay}}">次日</text>
                    </view>
                    <view class="time-range-duration" wx:if="{{item.duration}}">
                      <text class="duration-text">{{item.duration}}</text>
                    </view>
                  </view>
                  <view class="time-range-arrow">
                    <text class="arrow-icon">›</text>
                  </view>
                </view>
              </view>

              <!-- 收入设置区域 -->
              <view class="income-setting-row" wx:if="{{item.type !== 'rest'}}">
                <view class="income-field">
                  <text class="income-field-label">收入</text>
                  <view class="income-input-wrapper">
                    <input class="income-input-field"
                           type="digit"
                           placeholder="100"
                           value="{{item.incomeText || item.income}}"
                           bind:input="onIncomeChange"
                           data-index="{{index}}" />
                    <text class="income-unit">元</text>
                  </view>
                </view>

                <view class="hourly-rate-field">
                  <text class="hourly-rate-label">时薪</text>
                  <view class="hourly-rate-input-wrapper">
                    <input class="hourly-rate-input-field"
                           type="digit"
                           placeholder="60"
                           value="{{item.hourlyRateText || item.hourlyRate}}"
                           bind:input="onHourlyRateChange"
                           bind:blur="onHourlyRateBlur"
                           data-index="{{index}}" />
                    <text class="hourly-rate-unit">/小时</text>
                  </view>
                </view>
              </view>

              <!-- 时间段问题提示 -->
              <view class="segment-warning" wx:if="{{item.hasConflict}}">
                <text class="warning-icon">⚠️</text>
                <text class="warning-message">{{item.warningMessage || '时间设置有问题'}}</text>
              </view>
            </view>
          </view>

          <view class="add-time-btn" bind:tap="onAddTimeInput">
            <text class="add-icon">➕</text>
            <text>添加时间段</text>
          </view>
        </view>
      </view>

      <!-- 统计信息区域 -->
      <view class="schedule-summary">
        <view class="summary-item">
          <text class="summary-label">工作</text>
          <text class="summary-value">{{workHoursText}}</text>
        </view>
        <view class="summary-item">
          <text class="summary-label">休息</text>
          <text class="summary-value">{{restHoursText}}</text>
        </view>
        <view class="summary-item">
          <text class="summary-label">加班</text>
          <text class="summary-value">{{overtimeHoursText}}</text>
        </view>
        <view class="summary-item income-item">
          <text class="summary-label">收入</text>
          <text class="summary-value income-value">{{totalIncomeText}}元</text>
        </view>
      </view>

      <view class="modal-footer">
        <view class="btn-secondary modal-btn" bind:tap="onImportFromDateInModal">
          <text class="btn-icon">📥</text>
          <text>导入安排</text>
        </view>
        <view class="btn-primary modal-btn {{hasTimeConflict ? 'disabled' : ''}}"
              bind:tap="onConfirmSchedule">
          <text>{{hasTimeConflict ? '无法保存' : '保存'}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 批量操作模态框 -->
  <view class="modal {{batchModalVisible ? 'show' : ''}}" wx:if="{{showBatchModal}}" bind:tap="onCloseBatchModal">
    <view class="modal-content batch-modal-content" catch:tap="onStopPropagation">
      <view class="modal-header">
        <view class="modal-title">
          <text wx:if="{{batchOperation === 'import'}}">导入工作安排</text>
          <text wx:elif="{{batchOperation === 'copy' && batchStep === 1}}">选择源日期</text>
          <text wx:elif="{{batchOperation === 'copy' && batchStep === 2}}">选择目标日期</text>
        </view>
        <view class="modal-close" bind:tap="onCloseBatchModal">×</view>
      </view>
      
      <view class="modal-body">
        <!-- 导入模式 -->
        <view wx:if="{{batchOperation === 'import'}}">
          <view class="modal-subtitle">选择要导入的日期</view>
          <view class="template-list">
            <view class="template-item" wx:for="{{templateDates}}" wx:key="dateKey" bind:tap="onSelectTemplate" data-index="{{index}}">
              <view class="template-date">
                <text class="date-text">{{item.dateText}}</text>
                <view class="status-badge" style="background-color: {{item.statusConfig.backgroundColor}}; color: {{item.statusConfig.color}};">
                  <text class="status-icon">{{item.statusConfig.icon}}</text>
                  <text class="status-text">{{item.statusConfig.name}}</text>
                </view>
              </view>
              <view class="template-info">
                <text class="segment-count">{{item.segmentCount}}个时间段</text>
                <text class="income-info">收入: ¥{{item.dailyIncome.toFixed(2)}}</text>
              </view>
            </view>
          </view>
          
          <view wx:if="{{templateDates.length === 0}}" class="empty-templates">
            <view class="empty-icon">📅</view>
            <view class="empty-text">暂无可导入的日期</view>
            <view class="empty-tip">请先设置其他日期的工作安排</view>
          </view>
        </view>
        
        <!-- 批量复制模式 -->
        <view wx:else>
          <!-- 步骤1：选择源日期 -->
          <view wx:if="{{batchStep === 1}}">
            <view class="batch-step-info">
              <text class="step-text">第1步：选择源日期</text>
              <text class="step-desc">点击有工作安排的日期查看详情</text>
            </view>
            
            <!-- 批量复制日历 -->
            <view class="batch-calendar">
              <view class="batch-calendar-header">
                <view class="batch-calendar-nav" bind:tap="onBatchPreviousYear">
                  <text class="nav-arrow">‹‹</text>
                </view>
                <view class="batch-calendar-nav" bind:tap="onBatchPreviousMonth">
                  <text class="nav-arrow">‹</text>
                </view>
                <view class="batch-calendar-title">
                  <text>{{batchCalendarYear}}年{{batchCalendarMonth}}月</text>
                </view>
                <view class="batch-calendar-nav" bind:tap="onBatchNextMonth">
                  <text class="nav-arrow">›</text>
                </view>
                <view class="batch-calendar-nav" bind:tap="onBatchNextYear">
                  <text class="nav-arrow">››</text>
                </view>
              </view>
              
              <view class="batch-calendar-weekdays">
                <view class="weekday">日</view>
                <view class="weekday">一</view>
                <view class="weekday">二</view>
                <view class="weekday">三</view>
                <view class="weekday">四</view>
                <view class="weekday">五</view>
                <view class="weekday">六</view>
              </view>
              
              <view class="batch-calendar-days">
                <block wx:for="{{batchCalendarDays}}" wx:key="index">
                  <view class="batch-calendar-day {{item ? 'current-month' : 'empty'}} {{item && item.hasData ? 'has-data' : ''}} {{item && item.isSelectedSource ? 'selected-source' : ''}} {{item && !item.isInEmploymentRange ? 'day-outside-employment' : ''}}"
                        bind:tap="onBatchDateTap"
                        data-index="{{index}}"
                        wx:if="{{item}}">
                    <text class="day-number">{{item.day}}</text>
                    <view wx:if="{{item.hasData}}" class="day-indicator" style="background-color: {{item.statusConfig.backgroundColor}};">
                      <text class="indicator-text">{{item.segmentCount}}</text>
                    </view>
                  </view>
                  <view class="batch-calendar-day empty" wx:else></view>
                </block>
              </view>
            </view>
            
            <!-- 源日期工作安排预览 -->
            <view wx:if="{{sourceSchedulePreview}}" class="schedule-preview">
              <view class="preview-header">
                <text class="preview-title">{{sourceSchedulePreview.date}} 工作安排</text>
                <text class="preview-income">日收入: ¥{{sourceSchedulePreview.dailyIncome.toFixed(2)}}</text>
              </view>
              <view class="preview-segments">
                <view class="preview-segment" wx:for="{{sourceSchedulePreview.segments}}" wx:key="index">
                  <text class="segment-time">{{item.startTime}} - {{item.endTime}}</text>
                  <text class="segment-type">{{item.typeText}}</text>
                  <text class="segment-duration">{{item.duration}}</text>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 步骤2：选择目标日期 -->
          <view wx:elif="{{batchStep === 2}}">
            <view class="batch-step-info">
              <text class="step-text">第2步：选择目标日期</text>
              <text class="step-desc">点击选择要复制到的日期，已选择{{selectedTargetDates.length}}个日期</text>
            </view>
            
            <!-- 复制选项 -->
            <view class="copy-options">
              <view class="option-item">
                <text class="option-label">复制状态</text>
                <switch class="option-switch" checked="{{copyStatus}}" bind:change="onCopyStatusChange"/>
              </view>
              <view class="option-item">
                <text class="option-label">复制收入信息</text>
                <switch class="option-switch" checked="{{copyIncome}}" bind:change="onCopyIncomeChange"/>
              </view>
            </view>

            <!-- 快速选择工作日按钮 -->
            <view class="quick-select-section">
              <view class="quick-select-btn" bind:tap="onSelectAllWorkdays">
                <text class="quick-select-icon">📅</text>
                <text class="quick-select-text">选择该月全部工作日</text>
                <text class="quick-select-tip">
                  {{batchCalendarYear}}年{{batchCalendarMonth}}月共{{currentMonthWorkdays}}个工作日 • 已选择 {{selectedTargetDates.length}} 个日期
                </text>
                <view wx:if="{{isHolidayLoading}}" class="holiday-status">
                  <text class="status-text">🔄 正在加载节假日数据...</text>
                </view>
                <view wx:else class="holiday-status">
                  <text class="status-text">✅ 已根据节假日数据智能筛选工作日</text>
                </view>
              </view>
              <view wx:if="{{selectedTargetDates.length > 0}}" class="clear-selection-btn" bind:tap="onClearSelection">
                <text class="clear-icon">✖️</text>
                <text>清空选择</text>
              </view>
            </view>
            
            <!-- 批量复制日历 -->
            <view class="batch-calendar">
              <view class="batch-calendar-header">
                <view class="batch-calendar-nav" bind:tap="onBatchPreviousYear">
                  <text class="nav-arrow">‹‹</text>
                </view>
                <view class="batch-calendar-nav" bind:tap="onBatchPreviousMonth">
                  <text class="nav-arrow">‹</text>
                </view>
                <view class="batch-calendar-title">
                  <text>{{batchCalendarYear}}年{{batchCalendarMonth}}月</text>
                </view>
                <view class="batch-calendar-nav" bind:tap="onBatchNextMonth">
                  <text class="nav-arrow">›</text>
                </view>
                <view class="batch-calendar-nav" bind:tap="onBatchNextYear">
                  <text class="nav-arrow">››</text>
                </view>
              </view>
              
              <view class="batch-calendar-weekdays">
                <view class="weekday">日</view>
                <view class="weekday">一</view>
                <view class="weekday">二</view>
                <view class="weekday">三</view>
                <view class="weekday">四</view>
                <view class="weekday">五</view>
                <view class="weekday">六</view>
              </view>
              
              <view class="batch-calendar-days">
                <block wx:for="{{batchCalendarDays}}" wx:key="index">
                  <view class="batch-calendar-day {{item ? 'current-month' : 'empty'}} {{item && item.hasData ? 'has-data' : ''}} {{item && item.isSelectedTarget ? 'selected-target' : ''}} {{item && !item.isInEmploymentRange ? 'day-outside-employment' : ''}}"
                        bind:tap="onBatchDateTap"
                        data-index="{{index}}"
                        wx:if="{{item}}">
                    <text class="day-number">{{item.day}}</text>
                    <view wx:if="{{item.hasData}}" class="day-indicator" style="background-color: {{item.statusConfig.backgroundColor}};">
                      <text class="indicator-text">{{item.segmentCount}}</text>
                    </view>
                  </view>
                  <view class="batch-calendar-day empty" wx:else></view>
                </block>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <view class="modal-footer">
        <view class="btn-secondary modal-btn" bind:tap="onCloseBatchModal">
          <text>取消</text>
        </view>
        <view wx:if="{{batchOperation === 'copy' && batchStep === 2}}" class="btn-secondary modal-btn" bind:tap="onBackToSourceSelection">
          <text>上一步</text>
        </view>
        <view class="btn-primary modal-btn" bind:tap="{{batchOperation === 'import' ? 'onConfirmBatchOperation' : (batchStep === 1 ? 'onConfirmSourceDate' : 'onConfirmBatchOperation')}}">
          <text wx:if="{{batchOperation === 'import'}}">导入</text>
          <text wx:elif="{{batchStep === 1}}">下一步</text>
          <text wx:else>开始复制</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 导入安排模态框 -->
  <view class="modal" wx:if="{{showImportModal}}" bind:tap="onCloseImportModal">
    <view class="modal-content batch-modal-content" catch:tap="onStopPropagation">
      <view class="modal-header">
        <view class="modal-title">导入工作安排</view>
        <view class="modal-close" bind:tap="onCloseImportModal">×</view>
      </view>
      
      <view class="modal-body">
        
        <!-- 使用提示 -->
        <view class="import-tip">
          <text class="tip-icon">💡</text>
          <text class="tip-text">往下滚动可查看选择的日期安排详情</text>
        </view>
        
        <!-- 导入日历 -->
        <view class="batch-calendar">
          <view class="batch-calendar-header">
            <view class="batch-calendar-nav" bind:tap="onImportPreviousYear">
              <text class="nav-arrow">‹‹</text>
            </view>
            <view class="batch-calendar-nav" bind:tap="onImportPreviousMonth">
              <text class="nav-arrow">‹</text>
            </view>
            <view class="batch-calendar-title">
              <text>{{importCalendarYear}}年{{importCalendarMonth}}月</text>
            </view>
            <view class="batch-calendar-nav" bind:tap="onImportNextMonth">
              <text class="nav-arrow">›</text>
            </view>
            <view class="batch-calendar-nav" bind:tap="onImportNextYear">
              <text class="nav-arrow">››</text>
            </view>
          </view>
          
          <view class="batch-calendar-weekdays">
            <view class="weekday">日</view>
            <view class="weekday">一</view>
            <view class="weekday">二</view>
            <view class="weekday">三</view>
            <view class="weekday">四</view>
            <view class="weekday">五</view>
            <view class="weekday">六</view>
          </view>
          
          <view class="batch-calendar-days">
            <block wx:for="{{importCalendarDays}}" wx:key="index">
              <view class="batch-calendar-day {{item ? 'current-month' : 'empty'}} {{item && item.hasData ? 'has-data' : ''}} {{item && item.date === selectedImportDate ? 'selected' : ''}} {{item && !item.isInEmploymentRange ? 'day-outside-employment' : ''}}"
                    bind:tap="onImportDateTap"
                    data-index="{{index}}"
                    wx:if="{{item}}">
                <text class="day-number">{{item.day}}</text>
                <view wx:if="{{item.hasData}}" class="day-indicator" style="background-color: {{item.statusConfig.backgroundColor}};">
                  <text class="indicator-text">{{item.segmentCount}}</text>
                </view>
              </view>
              <view class="batch-calendar-day empty" wx:else></view>
            </block>
          </view>
        </view>
        
        <!-- 无数据提示 -->
        <view wx:if="{{!importHasAnyData}}" class="empty-templates">
          <view class="empty-icon">📅</view>
          <view class="empty-text">当前月份没有工作安排</view>
          <view class="empty-tip">请切换到其他月份或先设置工作安排</view>
        </view>
        
        <!-- 选中日期的详情 -->
        <view wx:if="{{selectedImportDateData}}" class="import-date-detail">
          <view class="detail-header">
            <text class="detail-title">{{selectedImportDate}}</text>
            <view class="detail-status">
              <text class="status-emoji">{{selectedImportDateData.statusConfig.icon}}</text>
              <text class="status-text">{{selectedImportDateData.statusConfig.text}}</text>
            </view>
          </view>
          
          <view class="detail-content">
            <view wx:if="{{selectedImportDateData.dailyIncome > 0}}" class="detail-income">
              <text class="income-label">当日收入：</text>
              <text class="income-value">¥{{selectedImportDateData.dailyIncome}}</text>
            </view>
            
            <view wx:if="{{selectedImportDateData.segments.length > 0}}" class="detail-segments">
              <text class="segments-label">时间安排：</text>
              <view class="segment-item" wx:for="{{selectedImportDateData.segments}}" wx:key="index">
                <text class="segment-type">{{item.type === 'work' ? '工作' : item.type === 'rest' ? '休息' : '加班'}}</text>
                <text class="segment-time">{{item.startTime}} - {{item.endTime}}</text>
              </view>
            </view>
            <view wx:else class="detail-segments">
              <text class="segments-label">时间安排：</text>
              <view class="no-segments-placeholder">
                <text class="placeholder-icon">📅</text>
                <text class="placeholder-text">没有添加任何时间安排</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <view class="modal-footer">
        <view class="btn-secondary modal-btn" bind:tap="onCloseImportModal">
          <text>取消</text>
        </view>
        <view class="btn-primary modal-btn {{selectedImportDate ? '' : 'disabled'}}" bind:tap="onConfirmImport">
          <text>确定导入</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 智能填写收入模态框 -->
  <view class="modal {{smartIncomeModalVisible ? 'show' : ''}}" wx:if="{{showSmartIncomeModal}}" bind:tap="onCloseSmartIncomeModal">
    <view class="modal-content smart-income-modal" catch:tap="onStopPropagation">
      <view class="modal-header">
        <view class="modal-title">智能填写收入</view>
        <view class="modal-close" bind:tap="onCloseSmartIncomeModal">×</view>
      </view>

      <view class="modal-body">
        <!-- 计算模式选择 -->
        <view class="input-group">
          <view class="input-label">计算方式</view>
          <view class="calculation-modes">
            <view class="mode-option {{smartIncomeMode === 'total' ? 'active' : ''}}"
                  bind:tap="onSelectIncomeMode"
                  data-mode="total">
              <text class="mode-icon">💰</text>
              <text class="mode-title">总收入分配</text>
              <text class="mode-desc">按时间比例分配总收入</text>
            </view>

            <view class="mode-option {{smartIncomeMode === 'overtime' ? 'active' : ''}}"
                  bind:tap="onSelectIncomeMode"
                  data-mode="overtime">
              <text class="mode-icon">⏰</text>
              <text class="mode-title">加班倍率计算</text>
              <text class="mode-desc">设置基础时薪和加班倍率</text>
            </view>

            <view class="mode-option {{smartIncomeMode === 'hourly' ? 'active' : ''}}"
                  bind:tap="onSelectIncomeMode"
                  data-mode="hourly">
              <text class="mode-icon">🕐</text>
              <text class="mode-title">分类时薪设置</text>
              <text class="mode-desc">为不同类型设置时薪</text>
            </view>
          </view>
        </view>

        <!-- 总收入分配模式 -->
        <view class="calculation-content" wx:if="{{smartIncomeMode === 'total'}}">
          <view class="input-group">
            <view class="input-with-calculator">
              <view class="input-label">当日总收入</view>
              <view class="daily-income-calculator-btn" bind:tap="onOpenDailyIncomeCalculator">
                <text class="calculator-icon">✨</text>
                <text class="calculator-text">计算日收入</text>
              </view>
            </view>
            <view class="input-with-unit">
              <input class="number-input"
                     type="digit"
                     placeholder="请输入总收入"
                     value="{{smartIncomeTotalAmountText || smartIncomeTotalAmount}}"
                     bind:input="onSmartIncomeTotalChange"/>
              <text class="input-unit">元</text>
            </view>
          </view>
        </view>

        <!-- 加班倍率模式 -->
        <view class="calculation-content" wx:if="{{smartIncomeMode === 'overtime'}}">
          <!-- 计算方式选择 -->
          <view class="calculation-method-selector">
            <view class="method-option {{smartIncomeOvertimeCalculationMethod === 'hourly' ? 'active' : ''}}"
                  bind:tap="onSelectSmartIncomeOvertimeCalculationMethod"
                  data-method="hourly">
              <text class="method-icon">💰</text>
              <text class="method-title">基础时薪</text>
              <text class="method-desc">输入基础时薪和倍率</text>
            </view>

            <view class="method-option {{smartIncomeOvertimeCalculationMethod === 'total' ? 'active' : ''}}"
                  bind:tap="onSelectSmartIncomeOvertimeCalculationMethod"
                  data-method="total">
              <text class="method-icon">📊</text>
              <text class="method-title">总收入</text>
              <text class="method-desc">输入总收入和倍率</text>
            </view>
          </view>

          <!-- 基础时薪方式 -->
          <view wx:if="{{smartIncomeOvertimeCalculationMethod === 'hourly'}}">
            <view class="input-group">
              <view class="input-label">基础时薪</view>
              <view class="input-with-unit">
                <input class="number-input"
                       type="digit"
                       placeholder="请输入基础时薪"
                       value="{{smartIncomeBaseHourlyText || smartIncomeBaseHourly}}"
                       bind:input="onSmartIncomeBaseHourlyChange" />
                <text class="input-unit">元/小时</text>
              </view>
            </view>
          </view>

          <!-- 总收入方式 -->
          <view wx:if="{{smartIncomeOvertimeCalculationMethod === 'total'}}">
            <view class="input-group">
              <view class="input-with-calculator">
                <view class="input-label">当日总收入</view>
                <view class="daily-income-calculator-btn" bind:tap="onOpenDailyIncomeCalculatorForOvertime">
                  <text class="calculator-icon">✨</text>
                  <text class="calculator-text">计算日收入</text>
                </view>
              </view>
              <view class="input-with-unit">
                <input class="number-input"
                       type="digit"
                       placeholder="请输入总收入"
                       value="{{smartIncomeOvertimeTotalAmountText || smartIncomeOvertimeTotalAmount}}"
                       bind:input="onSmartIncomeOvertimeTotalChange"/>
                <text class="input-unit">元</text>
              </view>
            </view>
          </view>

          <!-- 加班倍率 -->
          <view class="input-group">
            <view class="input-label">加班倍率</view>
            <view class="input-with-unit">
              <input class="number-input"
                     type="digit"
                     placeholder="请输入加班倍率"
                     value="{{smartIncomeOvertimeRateText || smartIncomeOvertimeRate}}"
                     bind:input="onSmartIncomeOvertimeRateChange" />
              <text class="input-unit">倍</text>
            </view>
          </view>
        </view>

        <!-- 分类时薪模式 -->
        <view class="calculation-content" wx:if="{{smartIncomeMode === 'hourly'}}">
          <view class="input-group">
            <view class="input-label">工作时薪</view>
            <view class="input-with-unit">
              <input class="number-input"
                     type="digit"
                     placeholder="请输入工作时薪"
                     value="{{smartIncomeWorkHourlyText || smartIncomeWorkHourly}}"
                     bind:input="onSmartIncomeWorkHourlyChange" />
              <text class="input-unit">元/小时</text>
            </view>
          </view>

          <view class="input-group">
            <view class="input-label">加班时薪</view>
            <view class="input-with-unit">
              <input class="number-input"
                     type="digit"
                     placeholder="请输入加班时薪"
                     value="{{smartIncomeOvertimeHourlyText || smartIncomeOvertimeHourly}}"
                     bind:input="onSmartIncomeOvertimeHourlyChange" />
              <text class="input-unit">元/小时</text>
            </view>
          </view>
        </view>
      </view>

      <view class="modal-footer">
        <view class="btn-secondary modal-btn" bind:tap="onCloseSmartIncomeModal">
          <text>取消</text>
        </view>
        <view class="btn-primary modal-btn" bind:tap="onConfirmSmartIncome">
          <text>应用计算</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 日收入计算器模态框 -->
  <view class="modal {{dailyIncomeCalculatorModalVisible ? 'show' : ''}}" wx:if="{{showDailyIncomeCalculatorModal}}" bind:tap="onCloseDailyIncomeCalculator">
    <view class="modal-content daily-income-calculator-modal" catch:tap="onStopPropagation">
      <view class="modal-header">
        <view class="modal-title">日收入计算器</view>
        <view class="modal-close" bind:tap="onCloseDailyIncomeCalculator">×</view>
      </view>

      <view class="modal-body">
        <view class="calculator-description">
          <text class="description-text">💡 通常双休为 21.75 天，单休为 26 天</text>
        </view>

        <view class="input-group-flex">
          <!-- 月收入 -->
          <view class="input-group">
            <view class="input-label">月收入</view>
            <view class="input-with-unit">
              <input class="number-input"
                     type="digit"
                     placeholder="10000"
                     value="{{dailyIncomeCalculatorMonthlyIncomeText || dailyIncomeCalculatorMonthlyIncome}}"
                     bind:input="onDailyIncomeMonthlyIncomeChange"/>
              <text class="input-unit">元</text>
            </view>
          </view>

          <!-- 月工作天数 -->
          <view class="input-group">
            <view class="input-label">月工作天数</view>
            <view class="input-with-unit">
              <input class="number-input"
                     type="digit"
                     placeholder="21.75"
                     value="{{dailyIncomeCalculatorWorkDaysText || dailyIncomeCalculatorWorkDays}}"
                     bind:input="onDailyIncomeWorkDaysChange"/>
              <text class="input-unit">天</text>
            </view>
          </view>
        </view>

        <view class="calculation-result">
          <view class="result-label">计算结果</view>
          <view class="result-value">
            <text class="result-amount">{{dailyIncomeCalculatorResult}}</text>
            <text class="result-unit">元/天</text>
          </view>
        </view>
      </view>

      <view class="modal-footer">
        <view class="btn-secondary modal-btn" bind:tap="onCloseDailyIncomeCalculator">
          <text>取消</text>
        </view>
        <view class="btn-primary modal-btn" bind:tap="onConfirmDailyIncomeCalculator">
          <text>确定使用</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 摸鱼记录编辑器 -->
  <fishing-editor
    visible="{{showFishingEditor}}"
    mode="{{fishingEditorMode}}"
    fishing-data="{{editingFishing}}"
    segments="{{displaySegments}}"
    bind:save="onFishingEditorSave"
    bind:delete="onFishingEditorDelete"
    bind:cancel="onFishingEditorCancel"
    bind:close="onFishingEditorClose">
  </fishing-editor>

  <!-- 日期类型选择器 -->
  <date-type-selector
    show="{{showDateTypeSelector}}"
    value="{{dateStatus}}"
    title="选择日期类型"
    bind:select="onDateTypeSelectorSelect"
    bind:confirm="onDateTypeSelectorConfirm"
    bind:cancel="onDateTypeSelectorCancel"
    bind:close="onDateTypeSelectorClose">
  </date-type-selector>

  <!-- 时间段选择器 -->
  <time-range-picker
    show="{{showTimeRangePicker}}"
    start-time="{{editingTimeIndex >= 0 ? timeInputs[editingTimeIndex].startTime : '09:00'}}"
    end-time="{{editingTimeIndex >= 0 ? timeInputs[editingTimeIndex].endTime : '18:00'}}"
    is-start-next-day="{{editingTimeIndex >= 0 ? timeInputs[editingTimeIndex].isStartNextDay : false}}"
    is-end-next-day="{{editingTimeIndex >= 0 ? timeInputs[editingTimeIndex].isEndNextDay : false}}"
    title="设置时间段"
    bind:confirm="onTimeRangePickerConfirm"
    bind:cancel="onTimeRangePickerCancel"
    bind:close="onTimeRangePickerClose">
  </time-range-picker>

  <!-- 收入调整模态框 -->
  <income-adjustment-modal
    visible="{{showIncomeAdjustmentModal}}"
    mode="{{adjustmentModalMode}}"
    date-string="{{adjustmentModalDateString}}"
    is-edit="{{adjustmentModalIsEdit}}"
    edit-item="{{adjustmentModalEditItem}}"
    bind:success="onIncomeAdjustmentSuccess"
    bind:error="onIncomeAdjustmentError"
    bind:close="onIncomeAdjustmentModalClose">
  </income-adjustment-modal>
</view>