/**
 * 时间段切换功能测试
 * 测试仪表盘1组件的时间段切换处理逻辑
 */

// 模拟微信小程序环境
global.wx = {
  showToast: function(options) {
    console.log('[Mock wx.showToast]', options)
  }
}

// 模拟组件数据和方法
function createMockDashboard1Component() {
  const component = {
    data: {
      hasWorkHistory: true,
      currentWorkId: 'test-work-1',
      todaySegments: [
        { id: 1, start: 540, end: 720, type: 'work', income: 100 }, // 09:00-12:00 工作
        { id: 2, start: 720, end: 780, type: 'rest', income: 0 },   // 12:00-13:00 休息
        { id: 3, start: 780, end: 1080, type: 'work', income: 150 } // 13:00-18:00 工作
      ],
      isFishing: false,
      currentFishingState: null,
      lastSegmentId: null,
      lastSegmentType: null,
      lastIsInWorkTime: null
    },

    setData: function(newData) {
      Object.assign(this.data, newData)
      console.log('[Mock setData]', newData)
    },

    // 模拟必要的方法
    getCurrentTimeSegment: function(now) {
      const currentMinutes = now.getHours() * 60 + now.getMinutes()
      return this.data.todaySegments.find(segment => {
        return currentMinutes >= segment.start && currentMinutes <= segment.end
      })
    },

    checkIsInWorkTime: function(now) {
      const currentMinutes = now.getHours() * 60 + now.getMinutes()
      const workSegment = this.data.todaySegments.find(segment => {
        return segment.type !== 'rest' &&
               currentMinutes >= segment.start &&
               currentMinutes <= segment.end
      })
      console.log(`[checkIsInWorkTime] 时间: ${now.getHours()}:${now.getMinutes()}, 分钟数: ${currentMinutes}, 工作时间段: ${workSegment ? workSegment.type : 'none'}`)
      return !!workSegment
    },

    loadTodaySchedule: function() {
      console.log('[Mock loadTodaySchedule] 重新加载时间段数据')
    },

    updateCurrentIncome: function(now) {
      console.log('[Mock updateCurrentIncome] 更新实时收入')
    },

    restartCurrentSegmentTimer: function() {
      console.log('[Mock restartCurrentSegmentTimer] 重启时间段定时器')
    },

    autoEndFishingOnTransition: function(now) {
      console.log('[Mock autoEndFishingOnTransition] 自动结束摸鱼')
      this.setData({
        isFishing: false,
        currentFishingState: null
      })
    },

    shouldAutoEndFishingOnTransition: function(previousState, currentState) {
      // 从工作时间段切换到非工作时间段，自动结束摸鱼
      if (previousState.isInWorkTime && !currentState.isInWorkTime) {
        return true
      }
      // 切换到休息时间段，结束摸鱼
      if (currentState.segment && currentState.segment.type === 'rest') {
        return true
      }
      return false
    },

    showTimeSegmentTransitionTip: function(previousState, currentState) {
      console.log('[Mock showTimeSegmentTransitionTip]', { previousState, currentState })
    }
  }

  // 添加实际的方法实现
  component.detectTimeSegmentChange = function(now) {
    if (!this.data.hasWorkHistory || !this.data.todaySegments || this.data.todaySegments.length === 0) {
      return
    }

    const currentSegment = this.getCurrentTimeSegment(now)
    const isInWorkTime = this.checkIsInWorkTime(now)

    // 检查是否发生了时间段切换
    const segmentChanged = this.data.lastSegmentId !== (currentSegment ? currentSegment.id : null)
    const workTimeChanged = this.data.lastIsInWorkTime !== null && this.data.lastIsInWorkTime !== isInWorkTime

    if (segmentChanged || workTimeChanged) {
      console.log('[detectTimeSegmentChange] 检测到时间段变化:', {
        previousSegmentId: this.data.lastSegmentId,
        currentSegmentId: currentSegment ? currentSegment.id : null,
        previousIsInWorkTime: this.data.lastIsInWorkTime,
        currentIsInWorkTime: isInWorkTime,
        segmentChanged,
        workTimeChanged
      })

      // 处理时间段切换
      this.handleTimeSegmentTransition(
        { id: this.data.lastSegmentId, type: this.data.lastSegmentType, isInWorkTime: this.data.lastIsInWorkTime },
        { segment: currentSegment, isInWorkTime: isInWorkTime },
        now
      )
    }

    // 更新状态记录
    this.setData({
      lastSegmentId: currentSegment ? currentSegment.id : null,
      lastSegmentType: currentSegment ? currentSegment.type : null,
      lastIsInWorkTime: isInWorkTime
    })
  }

  component.handleTimeSegmentTransition = function(previousState, currentState, now) {
    console.log('[handleTimeSegmentTransition] 处理时间段切换:', { previousState, currentState })

    // 1. 检查是否需要自动结束摸鱼
    if (this.data.isFishing && this.data.currentFishingState) {
      const shouldEndFishing = this.shouldAutoEndFishingOnTransition(previousState, currentState)
      
      if (shouldEndFishing) {
        console.log('[handleTimeSegmentTransition] 时间段切换触发自动结束摸鱼')
        this.autoEndFishingOnTransition(now)
      }
    }

    // 2. 重新加载时间段数据以确保状态同步
    this.loadTodaySchedule()

    // 3. 更新实时收入计算
    this.updateCurrentIncome(now)

    // 4. 重启时间段定时器
    this.restartCurrentSegmentTimer()

    // 5. 显示时间段切换提示
    this.showTimeSegmentTransitionTip(previousState, currentState)
  }

  return component
}

// 测试用例
function runTests() {
  console.log('=== 时间段切换功能测试开始 ===\n')

  const component = createMockDashboard1Component()

  // 测试1: 初始化状态
  console.log('测试1: 初始化状态')
  const time1 = new Date()
  time1.setHours(10, 30, 0, 0) // 10:30 - 工作时间段
  component.detectTimeSegmentChange(time1)
  console.log('初始状态设置完成\n')

  // 测试2: 从工作时间段切换到休息时间段
  console.log('测试2: 从工作时间段切换到休息时间段')
  component.setData({ isFishing: true, currentFishingState: { isActive: true } })
  const time2 = new Date()
  time2.setHours(12, 30, 0, 0) // 12:30 - 休息时间段中间
  component.detectTimeSegmentChange(time2)
  console.log('应该自动结束摸鱼（因为切换到休息时间段）\n')

  // 测试3: 从休息时间段切换到工作时间段
  console.log('测试3: 从休息时间段切换到工作时间段')
  const time3 = new Date()
  time3.setHours(13, 0, 0, 0) // 13:00 - 工作时间段
  component.detectTimeSegmentChange(time3)
  console.log('应该更新按钮状态\n')

  // 测试4: 在同一工作时间段内，不应该触发切换
  console.log('测试4: 在同一工作时间段内移动')
  const time4 = new Date()
  time4.setHours(13, 30, 0, 0) // 13:30 - 仍在同一工作时间段
  component.detectTimeSegmentChange(time4)
  console.log('不应该触发时间段切换\n')

  // 测试5: 从工作时间段切换到下班时间
  console.log('测试5: 从工作时间段切换到下班时间')
  component.setData({ isFishing: true, currentFishingState: { isActive: true } })
  const time5 = new Date()
  time5.setHours(18, 30, 0, 0) // 18:30 - 下班时间（超出所有时间段）
  component.detectTimeSegmentChange(time5)
  console.log('应该自动结束摸鱼\n')

  console.log('=== 时间段切换功能测试完成 ===')
}

// 运行测试
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runTests, createMockDashboard1Component }
} else {
  runTests()
}
