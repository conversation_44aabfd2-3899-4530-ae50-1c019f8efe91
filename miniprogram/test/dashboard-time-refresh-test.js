/**
 * 仪表盘时间刷新功能测试
 * 测试页面切换时时间显示是否正常刷新
 */

// 模拟微信小程序环境
const mockWx = {
  getWindowInfo: () => ({ statusBarHeight: 20 }),
  cloud: {
    callFunction: () => Promise.resolve({ result: { success: true, data: { count: 10 } } })
  },
  showToast: () => {}
}

// 模拟全局 wx 对象
global.wx = mockWx

// 模拟组件数据和方法
function createMockComponent() {
  return {
    data: {
      autoRefresh: true,
      refreshInterval: 1000,
      currentTime: '00:00:00',
      currentDate: new Date(),
      isFishing: false,
      showFishingCount: true
    },
    refreshTimer: null,
    
    setData: function(data) {
      Object.assign(this.data, data)
      console.log('setData called with:', data)
    },
    
    updateCurrentTime: function() {
      const now = new Date()
      this.setData({
        currentTime: now.toTimeString().substring(0, 8)
      })
      console.log('updateCurrentTime called, new time:', this.data.currentTime)
    },
    
    startAutoRefresh: function() {
      // 如果定时器已经在运行，先停止
      if (this.refreshTimer) {
        this.stopAutoRefresh()
      }
      
      this.refreshTimer = setInterval(() => {
        this.updateCurrentTime()
      }, this.data.refreshInterval)
      console.log('startAutoRefresh called, timer started')
    },
    
    stopAutoRefresh: function() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
        console.log('stopAutoRefresh called, timer stopped')
      }
    },
    
    updateCurrentFishingState: function() {
      console.log('updateCurrentFishingState called')
    },
    
    startCountdownTimer: function() {
      console.log('startCountdownTimer called')
    },
    
    updateFishingCount: function() {
      console.log('updateFishingCount called')
    },
    
    startFishingToggleTimer: function() {
      console.log('startFishingToggleTimer called')
    },
    
    cleanup: function() {
      this.stopAutoRefresh()
      console.log('cleanup called')
    }
  }
}

// 测试仪表盘1的页面生命周期
function testDashboard1PageLifetimes() {
  console.log('\n=== 测试仪表盘1页面生命周期 ===')
  
  const component = createMockComponent()
  
  // 模拟页面显示
  console.log('\n1. 模拟页面显示 (show)')
  const oldTime = component.data.currentTime
  
  // 执行页面显示逻辑（修复后的代码）
  component.updateCurrentTime()
  
  if (component.data.autoRefresh) {
    component.stopAutoRefresh()
    component.startAutoRefresh()
  }
  
  component.updateCurrentFishingState()
  component.startCountdownTimer()
  component.updateFishingCount()
  
  if (component.data.isFishing) {
    component.startFishingToggleTimer()
  }
  
  console.log('时间是否更新:', oldTime !== component.data.currentTime)
  console.log('定时器是否启动:', component.refreshTimer !== null)
  
  // 模拟页面隐藏
  console.log('\n2. 模拟页面隐藏 (hide)')
  component.cleanup()
  console.log('定时器是否停止:', component.refreshTimer === null)
}

// 测试仪表盘2的页面生命周期
function testDashboard2PageLifetimes() {
  console.log('\n=== 测试仪表盘2页面生命周期 ===')
  
  const component = createMockComponent()
  
  // 模拟页面显示
  console.log('\n1. 模拟页面显示 (show)')
  const oldTime = component.data.currentTime
  
  // 执行页面显示逻辑（修复后的代码）
  component.updateCurrentTime()
  
  if (component.data.autoRefresh) {
    component.stopAutoRefresh()
    component.startAutoRefresh()
  }
  
  component.updateFishingCount()
  
  console.log('时间是否更新:', oldTime !== component.data.currentTime)
  console.log('定时器是否启动:', component.refreshTimer !== null)
  
  // 模拟页面隐藏
  console.log('\n2. 模拟页面隐藏 (hide)')
  component.cleanup()
  console.log('定时器是否停止:', component.refreshTimer === null)
}

// 测试定时器重复启动保护
function testTimerProtection() {
  console.log('\n=== 测试定时器重复启动保护 ===')
  
  const component = createMockComponent()
  
  // 启动第一个定时器
  component.startAutoRefresh()
  const firstTimer = component.refreshTimer
  console.log('第一个定时器ID:', firstTimer)
  
  // 尝试启动第二个定时器
  component.startAutoRefresh()
  const secondTimer = component.refreshTimer
  console.log('第二个定时器ID:', secondTimer)
  
  console.log('定时器是否被正确替换:', firstTimer !== secondTimer)
  
  // 清理
  component.stopAutoRefresh()
}

// 运行所有测试
function runAllTests() {
  console.log('开始测试仪表盘时间刷新功能...')
  
  testDashboard1PageLifetimes()
  testDashboard2PageLifetimes()
  testTimerProtection()
  
  console.log('\n=== 测试完成 ===')
  console.log('如果所有测试都显示正确的结果，说明修复成功！')
}

// 导出测试函数
module.exports = {
  runAllTests,
  testDashboard1PageLifetimes,
  testDashboard2PageLifetimes,
  testTimerProtection
}

// 如果直接运行此文件，执行所有测试
if (require.main === module) {
  runAllTests()
}
